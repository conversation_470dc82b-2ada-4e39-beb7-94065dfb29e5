{"anchors": [10, 13, 16, 30, 33, 23, 30, 61, 62, 45, 59, 119, 116, 90, 156, 198, 373, 326], "inputs": {"input0": [224, 224, 3]}, "outputs": {"output0": [28, 28, 18], "output1": [14, 14, 18], "output2": [7, 7, 18]}, "params_quantity": 7022326, "loss": [[1, 3.31753], [2, 2.28696], [3, 1.60516], [4, 1.25281], [5, 1.07397], [6, 0.9734], [7, 0.90493], [8, 0.88082], [9, 0.82016], [10, 0.79659], [11, 0.78468], [12, 0.75058], [13, 0.74011], [14, 0.74591], [15, 0.71874], [16, 0.70169], [17, 0.68679], [18, 0.6726], [19, 0.67112], [20, 0.65438], [21, 0.64164], [22, 0.6532], [23, 0.63266], [24, 0.62424], [25, 0.61393], [26, 0.6136], [27, 0.6107], [28, 0.59415], [29, 0.57438], [30, 0.59323], [31, 0.57223], [32, 0.56684], [33, 0.56112], [34, 0.55708], [35, 0.5517], [36, 0.54709], [37, 0.55048], [38, 0.53265], [39, 0.53318], [40, 0.51835], [41, 0.53721], [42, 0.50836], [43, 0.5094], [44, 0.50384], [45, 0.50577], [46, 0.49773], [47, 0.48554], [48, 0.48936], [49, 0.48858], [50, 0.48053], [51, 0.48586], [52, 0.47651], [53, 0.4737], [54, 0.46363], [55, 0.46351], [56, 0.46223], [57, 0.44769], [58, 0.45525], [59, 0.45192], [60, 0.45032], [61, 0.43247], [62, 0.4365], [63, 0.42841], [64, 0.43227], [65, 0.4195], [66, 0.42406], [67, 0.42859], [68, 0.42412], [69, 0.41759], [70, 0.40652], [71, 0.4115], [72, 0.40451], [73, 0.39507], [74, 0.39955], [75, 0.39528], [76, 0.38725], [77, 0.38973], [78, 0.39203], [79, 0.39746], [80, 0.39123], [81, 0.3803], [82, 0.37583], [83, 0.37502], [84, 0.37837], [85, 0.37782], [86, 0.37663], [87, 0.3702], [88, 0.36979], [89, 0.36141], [90, 0.36181], [91, 0.35821], [92, 0.36066], [93, 0.36673], [94, 0.35823], [95, 0.35369], [96, 0.3509], [97, 0.3438], [98, 0.3563], [99, 0.35242], [100, 0.35488]], "loss_conf": [[1, 0.00933], [2, 0.01313], [3, 0.0135], [4, 0.01162], [5, 0.00972], [6, 0.00857], [7, 0.00789], [8, 0.00738], [9, 0.00713], [10, 0.00681], [11, 0.00661], [12, 0.00641], [13, 0.00622], [14, 0.00608], [15, 0.00599], [16, 0.00586], [17, 0.00577], [18, 0.00565], [19, 0.00554], [20, 0.00546], [21, 0.00539], [22, 0.00529], [23, 0.00523], [24, 0.00515], [25, 0.00505], [26, 0.005], [27, 0.00498], [28, 0.00488], [29, 0.00479], [30, 0.00477], [31, 0.00469], [32, 0.00463], [33, 0.00458], [34, 0.00453], [35, 0.0045], [36, 0.00443], [37, 0.00439], [38, 0.00436], [39, 0.00428], [40, 0.00427], [41, 0.00425], [42, 0.0042], [43, 0.00416], [44, 0.0041], [45, 0.00407], [46, 0.00405], [47, 0.00397], [48, 0.00395], [49, 0.00394], [50, 0.00389], [51, 0.00389], [52, 0.00383], [53, 0.0038], [54, 0.00377], [55, 0.00374], [56, 0.00372], [57, 0.00367], [58, 0.00363], [59, 0.00363], [60, 0.0036], [61, 0.00356], [62, 0.00353], [63, 0.00351], [64, 0.00349], [65, 0.00345], [66, 0.00346], [67, 0.00343], [68, 0.00341], [69, 0.00339], [70, 0.00335], [71, 0.00332], [72, 0.0033], [73, 0.00326], [74, 0.00325], [75, 0.00324], [76, 0.00318], [77, 0.00318], [78, 0.00316], [79, 0.00315], [80, 0.00314], [81, 0.00309], [82, 0.00305], [83, 0.00303], [84, 0.00305], [85, 0.00305], [86, 0.00302], [87, 0.003], [88, 0.00298], [89, 0.00296], [90, 0.00293], [91, 0.0029], [92, 0.00291], [93, 0.00288], [94, 0.00286], [95, 0.00284], [96, 0.00282], [97, 0.0028], [98, 0.0028], [99, 0.00281], [100, 0.00277]], "loss_pos": [[1, 0.09682], [2, 0.06047], [3, 0.03825], [4, 0.0286], [5, 0.02494], [6, 0.02275], [7, 0.02147], [8, 0.02088], [9, 0.01963], [10, 0.01881], [11, 0.01878], [12, 0.01782], [13, 0.01751], [14, 0.01799], [15, 0.01743], [16, 0.01681], [17, 0.01649], [18, 0.01608], [19, 0.01619], [20, 0.01561], [21, 0.01531], [22, 0.01575], [23, 0.0152], [24, 0.01502], [25, 0.01469], [26, 0.01489], [27, 0.01475], [28, 0.0143], [29, 0.01369], [30, 0.01464], [31, 0.01372], [32, 0.01359], [33, 0.01344], [34, 0.01357], [35, 0.01357], [36, 0.01321], [37, 0.01343], [38, 0.01298], [39, 0.01285], [40, 0.01256], [41, 0.01331], [42, 0.01217], [43, 0.01232], [44, 0.01234], [45, 0.01262], [46, 0.012], [47, 0.01177], [48, 0.0119], [49, 0.0119], [50, 0.0116], [51, 0.01186], [52, 0.0117], [53, 0.01164], [54, 0.01112], [55, 0.01122], [56, 0.01114], [57, 0.01087], [58, 0.01111], [59, 0.01095], [60, 0.01095], [61, 0.01049], [62, 0.0105], [63, 0.01029], [64, 0.01061], [65, 0.01019], [66, 0.01036], [67, 0.01052], [68, 0.01037], [69, 0.01018], [70, 0.0098], [71, 0.00997], [72, 0.00983], [73, 0.00974], [74, 0.00971], [75, 0.00958], [76, 0.00939], [77, 0.00946], [78, 0.00942], [79, 0.00972], [80, 0.00963], [81, 0.00928], [82, 0.00908], [83, 0.0091], [84, 0.00925], [85, 0.00927], [86, 0.00909], [87, 0.00904], [88, 0.00899], [89, 0.0087], [90, 0.00878], [91, 0.00879], [92, 0.00879], [93, 0.00899], [94, 0.0088], [95, 0.00891], [96, 0.00854], [97, 0.00834], [98, 0.00887], [99, 0.00873], [100, 0.00868]], "loss_class": [[1, 0.0], [2, 0.0], [3, 0.0], [4, 0.0], [5, 0.0], [6, 0.0], [7, 0.0], [8, 0.0], [9, 0.0], [10, 0.0], [11, 0.0], [12, 0.0], [13, 0.0], [14, 0.0], [15, 0.0], [16, 0.0], [17, 0.0], [18, 0.0], [19, 0.0], [20, 0.0], [21, 0.0], [22, 0.0], [23, 0.0], [24, 0.0], [25, 0.0], [26, 0.0], [27, 0.0], [28, 0.0], [29, 0.0], [30, 0.0], [31, 0.0], [32, 0.0], [33, 0.0], [34, 0.0], [35, 0.0], [36, 0.0], [37, 0.0], [38, 0.0], [39, 0.0], [40, 0.0], [41, 0.0], [42, 0.0], [43, 0.0], [44, 0.0], [45, 0.0], [46, 0.0], [47, 0.0], [48, 0.0], [49, 0.0], [50, 0.0], [51, 0.0], [52, 0.0], [53, 0.0], [54, 0.0], [55, 0.0], [56, 0.0], [57, 0.0], [58, 0.0], [59, 0.0], [60, 0.0], [61, 0.0], [62, 0.0], [63, 0.0], [64, 0.0], [65, 0.0], [66, 0.0], [67, 0.0], [68, 0.0], [69, 0.0], [70, 0.0], [71, 0.0], [72, 0.0], [73, 0.0], [74, 0.0], [75, 0.0], [76, 0.0], [77, 0.0], [78, 0.0], [79, 0.0], [80, 0.0], [81, 0.0], [82, 0.0], [83, 0.0], [84, 0.0], [85, 0.0], [86, 0.0], [87, 0.0], [88, 0.0], [89, 0.0], [90, 0.0], [91, 0.0], [92, 0.0], [93, 0.0], [94, 0.0], [95, 0.0], [96, 0.0], [97, 0.0], [98, 0.0], [99, 0.0], [100, 0.0]], "lr": [[1, 0.001], [2, 0.00098], [3, 0.00095], [4, 0.00093], [5, 0.00091], [6, 0.00089], [7, 0.00087], [8, 0.00085], [9, 0.00083], [10, 0.00081], [11, 0.00079], [12, 0.00078], [13, 0.00076], [14, 0.00074], [15, 0.00072], [16, 0.00071], [17, 0.00069], [18, 0.00068], [19, 0.00066], [20, 0.00065], [21, 0.00063], [22, 0.00062], [23, 0.0006], [24, 0.00059], [25, 0.00058], [26, 0.00056], [27, 0.00055], [28, 0.00054], [29, 0.00052], [30, 0.00051], [31, 0.0005], [32, 0.00049], [33, 0.00048], [34, 0.00047], [35, 0.00046], [36, 0.00045], [37, 0.00044], [38, 0.00043], [39, 0.00042], [40, 0.00041], [41, 0.0004], [42, 0.00039], [43, 0.00038], [44, 0.00037], [45, 0.00036], [46, 0.00035], [47, 0.00035], [48, 0.00034], [49, 0.00033], [50, 0.00032], [51, 0.00032], [52, 0.00031], [53, 0.0003], [54, 0.0003], [55, 0.00029], [56, 0.00028], [57, 0.00028], [58, 0.00027], [59, 0.00026], [60, 0.00026], [61, 0.00025], [62, 0.00025], [63, 0.00024], [64, 0.00023], [65, 0.00023], [66, 0.00022], [67, 0.00022], [68, 0.00021], [69, 0.00021], [70, 0.0002], [71, 0.0002], [72, 0.00019], [73, 0.00019], [74, 0.00019], [75, 0.00018], [76, 0.00018], [77, 0.00017], [78, 0.00017], [79, 0.00017], [80, 0.00016], [81, 0.00016], [82, 0.00015], [83, 0.00015], [84, 0.00015], [85, 0.00014], [86, 0.00014], [87, 0.00014], [88, 0.00013], [89, 0.00013], [90, 0.00013], [91, 0.00013], [92, 0.00012], [93, 0.00012], [94, 0.00012], [95, 0.00011], [96, 0.00011], [97, 0.00011], [98, 0.00011], [99, 0.0001], [100, 0.0001]], "val_acc": [[10, 1.0], [20, 1.0], [30, 1.0], [40, 1.0], [50, 1.0], [60, 1.0], [70, 1.0], [80, 1.0], [90, 1.0], [100, 1.0]], "val_acc_kuang": [[10, 1.0], [20, 1.0], [30, 1.0], [40, 1.0], [50, 1.0], [60, 1.0], [70, 1.0], [80, 1.0], [90, 1.0], [100, 1.0]], "mean": 123.5, "std": 58.395, "label_type": "detection", "data_type": "image", "labels": ["kuang"], "best_eval": {"epoch": 100, "val_acc": 1.0, "val_info": [{"name": "2411ff5035904f948d9bb5432080955d.jpg", "nickname": "2 (714).jpg", "pred": [[116, 28, 228, 102, 0.9210893511772156, 0.0]], "gt": [[119, 26, 228, 106, 1.0, 0.0]], "ok": true}, {"name": "0194b1e72f30439b9f48f6490f6d49eb.jpg", "nickname": "2 (137).jpg", "pred": [[195, 50, 295, 120, 0.9075435996055603, 0.0]], "gt": [[197, 50, 290, 123, 1.0, 0.0]], "ok": true}, {"name": "2c4c05af2e254278a5d42121a813ae22.jpg", "nickname": "1 (122).jpg", "pred": [[33, 21, 328, 259, 0.9075188636779785, 0.0]], "gt": [[33, 28, 323, 249, 1.0, 0.0]], "ok": true}]}}