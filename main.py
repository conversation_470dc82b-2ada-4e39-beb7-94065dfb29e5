
from maix import camera, display, image, nn, app, comm,time
import struct, os
import cv2
import numpy as np
from maix.v1.machine import UART
import struct


# 串口初始化
uart = UART("/dev/ttyS0", 115200)
time.sleep_ms(100)

model_path = "model_228634.mud"
if not os.path.exists(model_path):
    model_path = "/root/models/my_test_module/228634/model_228634.mud"
detector = nn.YOLOv5(model=model_path)

cam = camera.Camera(width=320, height=240)
dis = display.Display()

####################串口收发####################
def uart_send(bullseye):
    x, y = bullseye
    data = struct.pack(">BHHB", 0xFE, x, y, 0xFD)
    uart.write(data)
    #print(data)
    print(f"Mid point: ({x},{y})")


####################用cv处理画面####################
def process_image_cv(img):
    img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
    img_gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    # 高斯滤波
    img_blur = cv2.GaussianBlur(img_gray, (5, 5), 0)
    edged = cv2.Canny(img_blur, 50, 100)
    contours, _ = cv2.findContours(edged, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        peri = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.01 * peri, True)
        if len(approx) == 4:
            area = cv2.contourArea(contour)
            if area > 500:
                rect = cv2.boundingRect(approx)
                x, y, w, h = rect
                cv2.rectangle(img_cv, (x, y), (x+w, y+h), (0, 255, 255), 2)
    return img_cv, contours

# 滤波状态变量
# 滤波状态变量
filtered_center = None
filtered_size = None

# 滤波参数配置
FILTER_ALPHA = 0.5  # 滤波系数 (0.1-0.5)，值越小越平滑
MIN_CONFIDENCE = 0.3  # 最低置信度阈值


while not app.need_exit():
    t_start = time.ticks_ms()

    img = cam.read()
    objs = detector.detect(img, conf_th = 0.2, iou_th = 0.3)
    
    # 选择置信度最高的物体
    best_obj = None
    if objs:
        best_obj = max(objs, key=lambda obj: obj.score)
    
    # 物体跟踪和滤波
    if best_obj:
        # 计算物体中心点
        center_x = best_obj.x + best_obj.w // 2
        center_y = best_obj.y + best_obj.h // 2
        
        # 计算物体尺寸
        size_w = best_obj.w 
        size_h = best_obj.h 
        
        # # 初始化滤波状态
        # if filtered_center is None:
        #     filtered_center = (center_x, center_y)
        #     filtered_size = (size_w, size_h)
        # else:
        #     # 应用指数移动平均滤波
        #     fx, fy = filtered_center
        #     filtered_center = (
        #         int(FILTER_ALPHA * center_x + (1 - FILTER_ALPHA) * fx),
        #         int(FILTER_ALPHA * center_y + (1 - FILTER_ALPHA) * fy)
        #     )
            
        #     # 对尺寸也进行滤波     
        #     fw, fh = filtered_size
        #     filtered_size = (
        #         int(FILTER_ALPHA * size_w + (1 - FILTER_ALPHA) * fw),  1
        #         int(FILTER_ALPHA * size_h + (1 - FILTER_ALPHA) * fh)
        #     )
        
        # 使用滤波后的中心点和尺寸
        # fx, fy = filtered_center
        # fw, fh = filtered_size
        fx, fy = (center_x, center_y)
        fw, fh = (size_w, size_h)       

        ccx =  fx - fw // 2 
        ccy =  fy - fh // 2     
        # 绘制物体框 - 修复参数顺序问题
        # 参数顺序: x, y, width, height, color, thickness
#       img.draw_rect(
#            ccx,  # x
#            ccy,  # y
#            fw,            # width
#            fh,            # height
#            image.COLOR_RED, 
#            1              # thickness
#       )

        ffw = int(fw * 1.25)
        if(ffw > 320): 
            ffw=320

        ffh =  int(fh * 1.5)
        if(ffh > 240): 
            ffh=240

        # 计算裁剪起始点，确保在图像范围内
        n_x = max(0, min(fx - ffw // 2, img.width() - ffw))
        n_y = max(0, min(fy - ffh // 2, img.height() - ffh))

        img_new = img.crop(n_x, n_y, ffw, ffh)
        img.draw_rect(n_x, n_y, ffw, ffh,image.COLOR_YELLOW,3)
        img_cv, contours = process_image_cv(img_new)

        # 用minAreaRect找圆环中点
        max_area = 0
        best_rect = None
        target_center = None
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 100:
                continue
            rect = cv2.minAreaRect(cnt)
            if area > max_area:
                max_area = area
                best_rect = rect
                center = (int(rect[0][0]), int(rect[0][1]))
                target_center = center 

        if best_rect is not None:
            center = target_center    

            box = cv2.boxPoints(best_rect)
            box = np.int0(box)
            for i in range(4): 
                start_point = (int(box[i][0]+n_x), int(box[i][1]+n_y ))
                end_point = (int(box[(i+1)%4][0]+n_x), int(box[(i+1)%4][1]+n_y))
                img.draw_line(start_point[0], start_point[1], end_point[0], end_point[1], color=image.COLOR_GREEN, thickness=2)
            # 绘制滤波后的中心点
            img.draw_circle(center[0]+n_x , center[1]+n_y , 5, color=image.COLOR_RED, thickness=-1)

            bullseye = (int(center[0]+n_x), int(center[1]+n_y))
            uart_send(bullseye)
            
            img.draw_string(10, 30, f"point: {center}", image.COLOR_WHITE, scale=0.8)
        
    else:
        # 没有检测到物体时重置滤波器
        filtered_center = None
        filtered_size = None

    t_end = time.ticks_ms()
    fps = 1000 / max(1, t_end - t_start)
    img.draw_string(0, 10, f"FPS: {fps:.1f}", image.COLOR_RED, scale=1)

    dis.show(img)

